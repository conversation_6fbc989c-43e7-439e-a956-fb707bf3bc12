cmake_minimum_required(VERSION 3.10)
project(Simple3DBuilder)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找 OCCT 库
find_package(OpenCASCADE REQUIRED)
find_package(PCL REQUIRED)
find_package(Eigen3 REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${OpenCASCADE_INCLUDE_DIR} ${PCL_INCLUDE_DIRS} ${EIGEN3_INCLUDE_DIR})
add_library(Simple3DBuilder STATIC Simple3DBuilder.cpp)
target_link_libraries(Simple3DBuilder PRIVATE ${OpenCASCADE_LIBRARIES})

# 添加可执行文件
add_executable(OpenBoxGenerator OpenBoxGenerator.cpp)
target_link_libraries(OpenBoxGenerator PRIVATE Simple3DBuilder ${OpenCASCADE_LIBRARIES} ${PCL_LIBRARIES} ${EIGEN3_LIBRARIES})

# 添加可执行文件
add_executable(ToolGenerator ToolGenerator.cpp)
target_link_libraries(ToolGenerator PRIVATE Simple3DBuilder ${OpenCASCADE_LIBRARIES} ${PCL_LIBRARIES} ${EIGEN3_LIBRARIES})

# 添加可执行文件
add_executable(StlToPlyTool StlToPlyTool.cpp)
target_link_libraries(StlToPlyTool PRIVATE Simple3DBuilder ${OpenCASCADE_LIBRARIES} ${PCL_LIBRARIES} ${EIGEN3_LIBRARIES})

# 添加可执行文件
add_executable(ModelJoiner ModelJoiner.cpp)
target_link_libraries(ModelJoiner PRIVATE Simple3DBuilder ${OpenCASCADE_LIBRARIES} ${PCL_LIBRARIES} ${EIGEN3_LIBRARIES})
