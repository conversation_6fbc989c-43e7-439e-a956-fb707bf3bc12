#include <iostream>
#include <getopt.h>
#include <string>
#include <stdexcept>
#include <filesystem>
#include <algorithm>
#include <sstream>
#include "Simple3DBuilder.h"

void print_usage() {
    std::cout << "用法: ./ModelJoiner [选项]\n"
              << "选项:\n"
              << "  -1, --input1 <path>             第一个输入模型文件路径 (STL/STEP/PLY) (必填)\n"
              << "  -2, --input2 <path>             第二个输入模型文件路径 (STL/STEP/PLY) (必填)\n"
              << "      --scale1 <value>            第一个模型缩放因子 (可选, 默认: 1.0)\n"
              << "      --scale2 <value>            第二个模型缩放因子 (可选, 默认: 1.0)\n"
              << "      --tx <value>                第二个模型X轴平移 (可选, 默认: 0.0)\n"
              << "      --ty <value>                第二个模型Y轴平移 (可选, 默认: 0.0)\n"
              << "      --tz <value>                第二个模型Z轴平移 (可选, 默认: 0.0)\n"
              << "      --rx <value>                第二个模型X轴旋转角度(弧度) (可选, 默认: 0.0)\n"
              << "      --ry <value>                第二个模型Y轴旋转角度(弧度) (可选, 默认: 0.0)\n"
              << "      --rz <value>                第二个模型Z轴旋转角度(弧度) (可选, 默认: 0.0)\n"
              << "  -n, --cloud-points <value>      PLY点云数量 (可选 默认: 100000)\n"
              << "  -o, --output-dir <path>         输出目录 (可选, 默认: 当前工作目录)\n"
              << "  -f, --output-filename <name>    输出文件名(不含扩展名) (可选, 默认: 自动生成)\n"
              << "  -s, --output-stl <value>        是否输出STL文件 (0=否, 1=是) (可选, 默认: 0)\n"
              << "\n说明:\n"
              << "  - 支持STL、STEP、PLY格式的模型文件拼接\n"
              << "  - 变换矩阵仅应用于第二个模型，第一个模型保持原位置\n"
              << "  - 旋转顺序为: 先绕X轴，再绕Y轴，最后绕Z轴\n";
}

int main(int argc, char* argv[]) {
    std::string input1_path = "";
    std::string input2_path = "";
    double scale1 = 1.0;
    double scale2 = 1.0;
    double tx = 0.0, ty = 0.0, tz = 0.0;
    double rx = 0.0, ry = 0.0, rz = 0.0;
    size_t cloud_points = 100000;
    std::string output_dir = ".";
    std::string output_filename = "";
    bool output_stl = false;

    const struct option long_options[] = {
        {"input1", required_argument, nullptr, '1'},
        {"input2", required_argument, nullptr, '2'},
        {"scale1", required_argument, nullptr, 1001},
        {"scale2", required_argument, nullptr, 1002},
        {"tx", required_argument, nullptr, 1003},
        {"ty", required_argument, nullptr, 1004},
        {"tz", required_argument, nullptr, 1005},
        {"rx", required_argument, nullptr, 1006},
        {"ry", required_argument, nullptr, 1007},
        {"rz", required_argument, nullptr, 1008},
        {"cloud-points", required_argument, nullptr, 'n'},
        {"output-dir", required_argument, nullptr, 'o'},
        {"output-filename", required_argument, nullptr, 'f'},
        {"output-stl", required_argument, nullptr, 's'},
        {nullptr, 0, nullptr, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "1:2:n:o:f:s:", long_options, nullptr)) != -1) {
        try {
            switch (opt) {
                case '1': input1_path = optarg ? optarg : ""; break;
                case '2': input2_path = optarg ? optarg : ""; break;
                case 1001: scale1 = std::stod(optarg); break;
                case 1002: scale2 = std::stod(optarg); break;
                case 1003: tx = std::stod(optarg); break;
                case 1004: ty = std::stod(optarg); break;
                case 1005: tz = std::stod(optarg); break;
                case 1006: rx = std::stod(optarg); break;
                case 1007: ry = std::stod(optarg); break;
                case 1008: rz = std::stod(optarg); break;
                case 'n': cloud_points = std::stoi(optarg); break;
                case 'o': output_dir = optarg ? optarg : "."; break;
                case 'f': output_filename = optarg ? optarg : ""; break;
                case 's': output_stl = std::stoi(optarg); break;
                default:
                    print_usage();
                    return 1;
            }
        } catch (const std::invalid_argument&) {
            std::cerr << "参数值无效，请输入有效数字。" << std::endl;
            return 1;
        }
    }

    // 参数验证
    if (input1_path.empty() || input2_path.empty()) {
        std::cerr << "错误：必须指定两个输入文件路径。" << std::endl;
        print_usage();
        return 1;
    }

    if (scale1 <= 0 || scale2 <= 0) {
        std::cerr << "错误：缩放因子必须大于0。" << std::endl;
        return 1;
    }

    if (cloud_points <= 0) {
        std::cerr << "错误：点云数量必须大于0。" << std::endl;
        return 1;
    }

    try {
        auto file = Simple3DBuilder::join_models(
            input1_path, input2_path,
            scale1, scale2,
            tx, ty, tz, rx, ry, rz,
            cloud_points, output_dir, output_stl, output_filename);
        
        std::cout << "模型拼接完成，输出文件: " << file << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "错误：" << e.what() << std::endl;
        return 1;
    }

    return 0;
}
