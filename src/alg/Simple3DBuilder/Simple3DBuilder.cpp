#include "Simple3DBuilder.h"

#include <pcl/io/ply_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

#include <BRepAdaptor_Curve.hxx>
#include <BRepAlgoAPI_Cut.hxx>
#include <BRepAlgoAPI_Fuse.hxx>
#include <BRepBuilderAPI_Transform.hxx>
#include <BRepFilletAPI_MakeFillet.hxx>
#include <BRepMesh_IncrementalMesh.hxx>
#include <BRepPrimAPI_MakeBox.hxx>
#include <BRepPrimAPI_MakeCone.hxx>
#include <BRepPrimAPI_MakeCylinder.hxx>
#include <BRepTools.hxx>
#include <BRep_Tool.hxx>
#include <Eigen/Dense>
#include <GCPnts_AbscissaPoint.hxx>
#include <Geom_Curve.hxx>
#include <Poly_Array1OfTriangle.hxx>
#include <Poly_Triangle.hxx>
#include <Poly_Triangulation.hxx>
#include <StlAPI_Reader.hxx>
#include <StlAPI_Writer.hxx>
#include <STEPControl_Reader.hxx>
#include <TopExp_Explorer.hxx>
#include <TopLoc_Location.hxx>
#include <TopoDS.hxx>
#include <TopoDS_Face.hxx>
#include <TopoDS_Shape.hxx>
#include <TopoDS_Solid.hxx>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <gp_Pnt.hxx>
#include <stdexcept>
#include <string>
#include <vector>
#include <set>
#include <limits>
#include <algorithm>
#include <thread>
#include <mutex>
#include <atomic>
#include <random>
#include <chrono>

Eigen::Vector3f sample_point_on_triangle(const Eigen::Vector3f& A, const Eigen::Vector3f& B, const Eigen::Vector3f& C) {
  float u = static_cast<float>(rand()) / RAND_MAX;
  float v = static_cast<float>(rand()) / RAND_MAX;
  if (u + v > 1.0f) {
    u = 1.0f - u;
    v = 1.0f - v;
  }
  float w = 1.0f - u - v;
  return u * A + v * B + w * C;
}

// 计算点到点的距离
float point_distance(const Eigen::Vector3f& p1, const Eigen::Vector3f& p2) {
  return (p1 - p2).norm();
}

void Simple3DBuilder::sample_shape_surface_to_ply(const TopoDS_Shape& shape, const std::string& ply_path, int target_total_points) {
  std::vector<std::tuple<Eigen::Vector3f, Eigen::Vector3f, Eigen::Vector3f, float>> triangles;
  float total_area = 0.0f;

  // 1. 遍历所有面并提取三角形
  for (TopExp_Explorer faceExp(shape, TopAbs_FACE); faceExp.More(); faceExp.Next()) {
    TopoDS_Face face = TopoDS::Face(faceExp.Current());
    TopLoc_Location loc;
    Handle(Poly_Triangulation) triangulation = BRep_Tool::Triangulation(face, loc);
    if (triangulation.IsNull()) continue;

    for (int i = 1; i <= triangulation->NbTriangles(); ++i) {
      Poly_Triangle tri = triangulation->Triangle(i);
      int n1, n2, n3;
      tri.Get(n1, n2, n3);

      if (n1 < 1 || n1 > triangulation->NbNodes() ||
          n2 < 1 || n2 > triangulation->NbNodes() ||
          n3 < 1 || n3 > triangulation->NbNodes()) {
        std::cerr << "三角形索引越界，跳过。" << std::endl;
        continue;
      }

      gp_Pnt p1 = triangulation->Node(n1).Transformed(loc.Transformation());
      gp_Pnt p2 = triangulation->Node(n2).Transformed(loc.Transformation());
      gp_Pnt p3 = triangulation->Node(n3).Transformed(loc.Transformation());

      Eigen::Vector3f v1(p1.X(), p1.Y(), p1.Z());
      Eigen::Vector3f v2(p2.X(), p2.Y(), p2.Z());
      Eigen::Vector3f v3(p3.X(), p3.Y(), p3.Z());

      // 计算面积
      float area = 0.5f * ((v2 - v1).cross(v3 - v1)).norm();
      total_area += area;
      triangles.emplace_back(v1, v2, v3, area);
    }
  }

  // 2. 面积加权采样
  pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>());

  // 使用多线程并行采样
  unsigned int num_threads = std::min(std::thread::hardware_concurrency(), 8u);
  if (num_threads == 0) num_threads = 4;

  // 计算每个三角形的采样点数
  std::vector<int> int_samples(triangles.size(), 0);
  int total_samples = 0;

  // 直接计算整数采样点数，避免浮点运算
  for (size_t i = 0; i < triangles.size(); ++i) {
    float area_ratio = std::get<3>(triangles[i]) / total_area;
    int samples = static_cast<int>(target_total_points * area_ratio);
    int_samples[i] = samples;
    total_samples += samples;
  }

  // 简单分配剩余点数给最大的三角形
  int remaining_to_assign = target_total_points - total_samples;
  if (remaining_to_assign > 0) {
    // 找到面积最大的几个三角形来分配剩余点数
    std::vector<std::pair<size_t, float>> area_indices;
    for (size_t i = 0; i < triangles.size(); ++i) {
      area_indices.emplace_back(i, std::get<3>(triangles[i]));
    }
    std::sort(area_indices.begin(), area_indices.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });

    for (int i = 0; i < remaining_to_assign && i < static_cast<int>(area_indices.size()); ++i) {
      int_samples[area_indices[i].first]++;
    }
  }

  // 多线程并行采样
  std::vector<std::thread> threads;
  std::mutex cloud_mutex;

  auto worker = [&](size_t start_idx, size_t end_idx) {
    std::vector<pcl::PointXYZ> local_points;
    local_points.reserve((end_idx - start_idx) * 10); // 预分配空间

    for (size_t i = start_idx; i < end_idx; ++i) {
      const auto& [v1, v2, v3, area] = triangles[i];
      for (int j = 0; j < int_samples[i]; ++j) {
        Eigen::Vector3f pt = sample_point_on_triangle(v1, v2, v3);
        local_points.emplace_back(pt.x(), pt.y(), pt.z());
      }
    }

    // 批量添加到主点云
    std::lock_guard<std::mutex> lock(cloud_mutex);
    cloud->points.insert(cloud->points.end(), local_points.begin(), local_points.end());
  };

  // 分配工作给线程
  size_t triangles_per_thread = triangles.size() / num_threads;
  for (unsigned int t = 0; t < num_threads; ++t) {
    size_t start_idx = t * triangles_per_thread;
    size_t end_idx = (t == num_threads - 1) ? triangles.size() : (t + 1) * triangles_per_thread;
    threads.emplace_back(worker, start_idx, end_idx);
  }

  // 等待所有线程完成
  for (auto& thread : threads) {
    thread.join();
  }

  // 3. 更新最终点云属性
  cloud->width = cloud->points.size();
  cloud->height = 1;
  cloud->is_dense = true;

  // 4. 写出为 PLY 文件
  pcl::io::savePLYFileBinary(ply_path, *cloud);
  std::cout << "PLY 文件已写入: " << ply_path << std::endl << "最终点数: " << cloud->points.size() << std::endl;
}

std::string Simple3DBuilder::generate_openbox(
    double inner_length,
    double inner_width,
    double inner_height,
    double outer_length,
    double outer_width,
    double outer_height,
    double corner_radius,
    size_t cloud_points,
    const std::string& output_dir,
    bool output_stl,
    const std::string& output_filename) {
  // 将毫米转换为米
  inner_length = inner_length;
  inner_width = inner_width;
  inner_height = inner_height;

  outer_length = outer_length;
  outer_width = outer_width;
  outer_height = outer_height;

  corner_radius = corner_radius;
  if (inner_length <= 0 || inner_width <= 0 || inner_height <= 0 || outer_length <= 0 || outer_width <= 0 || outer_height <= 0) {
    throw std::invalid_argument("内框和外框都尺寸必须大于0");
  }
  if (outer_length <= inner_length || outer_width <= inner_width || outer_height <= inner_height) {
    throw std::invalid_argument("外框尺寸必须大于内框尺寸");
  }
  if (corner_radius > 0 && corner_radius > (std::min(inner_width, inner_length))) {
    throw std::invalid_argument("倒角半径必须大于内框长和宽的一半");
  }

  // 创建外部箱体
  TopoDS_Solid outer_box = BRepPrimAPI_MakeBox(outer_length, outer_width, outer_height).Solid();

  // 创建内部空腔
  TopoDS_Solid inner_box = BRepPrimAPI_MakeBox(inner_length, inner_width, inner_height).Solid();

  // 平移内盒至外壳内部
  gp_Trsf translation;
  translation.SetTranslation(gp_Vec((outer_length - inner_length) / 2, (outer_width - inner_width) / 2, outer_height - inner_height));
  TopoDS_Shape inner_box_translated = BRepBuilderAPI_Transform(inner_box, translation).Shape();

  // 使用布尔运算切割成开口盒子
  TopoDS_Shape open_box = BRepAlgoAPI_Cut(outer_box, inner_box_translated).Shape();
  if (open_box.IsNull()) {
    throw std::runtime_error("开口盒几何体无效。");
  }

  // 倒角处理
  if (corner_radius > 0) {
    BRepFilletAPI_MakeFillet fillet(open_box);
    for (TopExp_Explorer exp(open_box, TopAbs_EDGE); exp.More(); exp.Next()) {
      TopoDS_Edge edge = TopoDS::Edge(exp.Current());
      gp_Pnt p1, p2;
      Standard_Real f, l;
      Handle(Geom_Curve) curve = BRep_Tool::Curve(edge, f, l);
      if (curve.IsNull()) continue;
      p1 = curve->Value(f);
      p2 = curve->Value(l);
      gp_Vec dir(p1, p2);

      // 仅对近似竖直的边进行倒角
      if (std::abs(dir.X()) < 1e-6 && std::abs(dir.Y()) < 1e-6 && std::abs(dir.Z()) > 1e-6) {
        try {
          fillet.Add(corner_radius, edge);
        } catch (...) {
          std::cerr << "警告：某个边倒角失败。" << std::endl;
        }
      }
    }
    open_box = fillet.Shape();
    if (open_box.IsNull()) {
      throw std::runtime_error("倒角失败，几何体无效。");
    }
  }

  // 平移至中心点(外框)
  translation.SetTranslation(gp_Vec(-outer_length / 2, -outer_width / 2, -outer_height / 2));
  TopoDS_Shape open_box_translated = BRepBuilderAPI_Transform(open_box, translation).Shape();

  std::filesystem::create_directories(output_dir);

  std::string name;
  if (output_filename.empty()) {
    // 使用自动生成的文件名
    name = "box_" + std::to_string((int)(inner_length * 1000)) + "x" + std::to_string((int)(inner_width * 1000)) + "x" + std::to_string((int)(inner_height * 1000)) + "_" + std::to_string((int)(outer_length * 1000)) + "x" + std::to_string((int)(outer_width * 1000)) + "x" + std::to_string((int)(outer_height * 1000));
    if (corner_radius > 0) {
      name += "_R" + std::to_string((int)(corner_radius * 1000));
    }
  } else {
    // 使用用户指定的文件名
    name = output_filename;
  }

  std::string stl_path = output_dir + "/" + name + ".stl";

  std::string ply_path = output_dir + "/" + name + ".ply";

  // 导出形状为STL和PLY文件（不缩放）
  export_shape(open_box_translated, stl_path, ply_path, cloud_points, output_stl, 0.001);

  return ply_path;
}

void Simple3DBuilder::sample_stl_to_ply(const std::string& stl_path, const std::string& ply_path, int target_total_points, double scale) {
    TopoDS_Shape shape = load_stl_shape(stl_path);
    shape_to_ply(shape, ply_path, target_total_points, scale);
}

std::string Simple3DBuilder::generate_robot_tool(
    int tool_type, double tool_diameter, double tool_length, double tip_length,
    int flange_type, double flange_diameter, double flange_height, double bolt_circle, int bolt_number,
    double bolt_radius, double center_hole_radius, double center_hole_length, double angle_offset,
    size_t cloud_points, const std::string& output_dir, bool output_stl, const std::string& output_filename) {

    std::filesystem::create_directories(output_dir);

    std::string base_filename;
    if (output_filename.empty()) {
        // 使用自动生成的文件名
        std::stringstream ss;
        std::string tool_name;
        switch (tool_type) {
            case 1: tool_name = "needle"; break;
            case 2: tool_name = "sucker"; break;
            case 3: tool_name = "gripper"; break;
            default: tool_name = "tool"; break;
        }

        ss << tool_name << "_d" << std::to_string((int)(tool_diameter)) << "_l" << std::to_string((int)(tool_length));
        if (tip_length > 0) ss << "_tip" << std::to_string((int)(tip_length));

        // 添加法兰信息
        if (flange_type == 0) {
            ss << "_no_flange";
        } else if (flange_type == 4) {
            ss << "_custom_flange_d" << std::to_string((int)(flange_diameter));
        } else {
            std::string flange_name;
            switch (flange_type) {
                case 1: flange_name = "KR3-10"; break;
                case 2: flange_name = "KR12R1450"; break;
                case 3: flange_name = "KR16-22"; break;
                default: flange_name = "flange" + std::to_string(flange_type); break;
            }
            ss << "_" << flange_name;
        }
        base_filename = ss.str();
    } else {
        // 使用用户指定的文件名
        base_filename = output_filename;
    }

    std::string ply_path = output_dir + "/" + base_filename + ".ply";
    std::string stl_path = output_dir + "/" + base_filename + ".stl";

    // 法兰参数结构
    struct FlangeParams {
        double diameter;
        double height;
        double bolt_circle;
        int bolt_number;
        double bolt_radius;
        double center_hole_radius;
        double center_hole_length;
        double angle_offset;
    };

    FlangeParams params;

    switch (flange_type) {
        case 0: // 无法兰
            params = {
                0.0,   // 法兰外径
                0.0,   // 法兰高度
                0.0,   // 螺栓直径
                0,     // 螺栓数量
                0.0,   // 螺栓孔半径
                0.0,   // 中心定位孔半径
                0.0,   // 中心定位孔长度
                0.0    // 角度偏移
            };
            break;
        case 1: // KR3-KR10
            params = {
                40.0,  // 法兰外径
                15.0,  // 法兰高度
                31.5,  // 螺栓直径
                8,     // 螺栓数量
                2.75,  // M5螺栓孔半径
                10.0,  // 中心定位孔半径
                6.0,   // 中心定位孔长度
                M_PI/4 // 45度偏移
            };
            break;
        case 2: // KR12R1450
            params = {
                65.0,   // 法兰外径
                15.0,   // 法兰高度
                58,     // 螺栓直径
                11,     // 螺栓数量
                2,      // M4螺栓孔半径
                25.5,   // 中心定位孔半径
                6.0,    // 中心定位孔长度
                M_PI/6  // 45度偏移
            };
            break;
        case 3: // KR16-KR22
            params = {
                63.0,  // 法兰外径
                15.0,  // 法兰高度
                50.0,  // 螺栓圆直径
                8,     // 螺栓数量
                3,     // 螺栓孔半径
                15.75, // 中心定位孔半径
                6.0,   // 中心定位孔长度
                M_PI/4 // 45度偏移
            };
            break;
        case 4: // 自定义法兰
            params = {
                flange_diameter,
                flange_height,
                bolt_circle,
                bolt_number,
                bolt_radius,
                center_hole_radius > 0 ? center_hole_radius : flange_diameter * 0.15,
                center_hole_length,
                angle_offset
            };
            break;
        default:
            throw std::invalid_argument("不支持的法兰类型");
    }

    // 使用标准法兰参数覆盖输入参数
    if (flange_type > 0 && flange_type != 4) {
        flange_diameter = params.diameter;
        flange_height = params.height;
        bolt_circle = params.bolt_circle;
        bolt_number = params.bolt_number;
    }

    TopoDS_Shape flange;
    bool has_flange = (flange_type > 0);

    if (has_flange) {
        // 创建法兰基本圆柱体
        gp_Ax2 flange_axis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
        flange = BRepPrimAPI_MakeCylinder(flange_axis, params.diameter/2, params.height).Shape();

        // 添加螺栓孔
        for (int i = 0; i < params.bolt_number; i++) {
            double angle = 2 * M_PI * i / params.bolt_number + params.angle_offset;
            double x = (params.bolt_circle/2) * cos(angle);
            double y = (params.bolt_circle/2) * sin(angle);
            gp_Ax2 bolt_axis(gp_Pnt(x, y, 0), gp_Dir(0, 0, 1));
            TopoDS_Shape bolt_hole = BRepPrimAPI_MakeCylinder(bolt_axis, params.bolt_radius, params.height).Shape();
            flange = BRepAlgoAPI_Cut(flange, bolt_hole).Shape();
        }

        // 添加中心孔
        gp_Ax2 center_axis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
        TopoDS_Shape center_hole = BRepPrimAPI_MakeCylinder(center_axis, params.center_hole_radius, params.center_hole_length).Shape();
        flange = BRepAlgoAPI_Cut(flange, center_hole).Shape();
    }

    // 调整工具长度，如果有法兰则减去法兰高度
    double actual_tool_length = has_flange ? (tool_length - params.height + params.center_hole_length) : tool_length;
    if (actual_tool_length <= 0) {
        throw std::invalid_argument(has_flange ? "工具长度必须大于法兰高度" : "工具长度必须大于0");
    }

    // 创建工具部分
    TopoDS_Shape tool;
    double tool_start_z = has_flange ? params.height : 0.0;

    switch (tool_type) {
        case 1: { // 针头工具
            // 如果未指定针尖长度，则默认为工具长度的20%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.2;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument(has_flange ? "针尖长度必须小于工具总长度减去法兰高度" : "针尖长度必须小于工具总长度");
            }

            // 创建针杆
            double shaft_length = actual_tool_length - tip_length;
            gp_Ax2 shaft_axis(gp_Pnt(0, 0, tool_start_z), gp_Dir(0, 0, 1));
            TopoDS_Shape shaft = BRepPrimAPI_MakeCylinder(shaft_axis, tool_diameter/2, shaft_length).Shape();

            // 创建针尖 - 使用更精细的网格参数确保针尖有足够的采样点
            gp_Ax2 tip_axis(gp_Pnt(0, 0, tool_start_z + shaft_length), gp_Dir(0, 0, 1));
            TopoDS_Shape tip = BRepPrimAPI_MakeCone(tip_axis, tool_diameter/2, 0.1, tip_length).Shape(); // 尖端不要完全为0

            // 组合针杆和针尖
            tool = BRepAlgoAPI_Fuse(shaft, tip).Shape();
            break;
        }
        case 2: { // 吸盘工具
            // 如果未指定吸盘高度，则默认为工具长度的30%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.3;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument(has_flange ? "吸盘高度必须小于工具总长度减去法兰高度" : "吸盘高度必须小于工具总长度");
            }

            // 创建吸盘杆
            double shaft_length = actual_tool_length - tip_length;
            gp_Ax2 shaft_axis(gp_Pnt(0, 0, tool_start_z), gp_Dir(0, 0, 1));
            // 使用合适的杆直径，如果有法兰则使用中心孔直径，否则使用工具直径的一半
            double shaft_radius = has_flange ? params.center_hole_radius : tool_diameter/4;
            TopoDS_Shape shaft = BRepPrimAPI_MakeCylinder(shaft_axis, shaft_radius, shaft_length).Shape();

            // 创建吸盘
            double cup_outer_radius = tool_diameter/2;
            double cup_inner_radius = cup_outer_radius * 0.8;
            gp_Ax2 cup_axis(gp_Pnt(0, 0, tool_start_z + shaft_length), gp_Dir(0, 0, 1));
            TopoDS_Shape cup_outer = BRepPrimAPI_MakeCylinder(cup_axis, cup_outer_radius, tip_length).Shape();
            gp_Ax2 cup_inner_axis(gp_Pnt(0, 0, tool_start_z + shaft_length + tip_length*0.2), gp_Dir(0, 0, 1));
            TopoDS_Shape cup_inner = BRepPrimAPI_MakeCylinder(cup_inner_axis, cup_inner_radius, tip_length*0.8).Shape();
            TopoDS_Shape cup = BRepAlgoAPI_Cut(cup_outer, cup_inner).Shape();

            // 组合吸盘杆和吸盘
            tool = BRepAlgoAPI_Fuse(shaft, cup).Shape();
            break;
        }
        case 3: { // 夹爪工具
            // 如果未指定夹爪臂长度，则默认为工具长度的70%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.7;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument(has_flange ? "夹爪臂长度必须小于工具总长度减去法兰高度" : "夹爪臂长度必须小于工具总长度");
            }

            // 创建夹爪基座
            double base_height = actual_tool_length - tip_length;
            gp_Ax2 base_axis(gp_Pnt(0, 0, tool_start_z), gp_Dir(0, 0, 1));
            TopoDS_Shape base = BRepPrimAPI_MakeCylinder(base_axis, tool_diameter*0.6/2, base_height).Shape();

            // 创建夹爪臂
            double arm_width = tool_diameter * 0.2;
            double arm_thickness = tool_diameter * 0.1;
            double arm_spacing = tool_diameter * 0.3;

            // 左臂
            TopoDS_Shape left_arm = BRepPrimAPI_MakeBox(
                gp_Pnt(-arm_width/2, -arm_spacing/2 - arm_thickness, tool_start_z + base_height),
                arm_width, arm_thickness, tip_length).Shape();

            // 右臂
            TopoDS_Shape right_arm = BRepPrimAPI_MakeBox(
                gp_Pnt(-arm_width/2, arm_spacing/2, tool_start_z + base_height),
                arm_width, arm_thickness, tip_length).Shape();

            // 组合基座和夹爪臂
            tool = BRepAlgoAPI_Fuse(base, left_arm).Shape();
            tool = BRepAlgoAPI_Fuse(tool, right_arm).Shape();
            break;
        }
        default:
            throw std::invalid_argument("不支持的工具类型");
    }

    // 组合法兰和工具（如果有法兰的话）
    TopoDS_Shape complete_tool;
    if (has_flange) {
        complete_tool = BRepAlgoAPI_Fuse(flange, tool).Shape();

        // 平移模型，使法兰中心位于原点
        gp_Trsf translation;
        translation.SetTranslation(gp_Vec(0, 0, -params.center_hole_length));
        BRepBuilderAPI_Transform transform(complete_tool, translation);
        complete_tool = transform.Shape();
    } else {
        // 没有法兰时，工具就是完整的工具
        complete_tool = tool;
    }

    // 导出形状为STL和PLY文件（包含缩放、网格化和采样）
    export_shape(complete_tool, stl_path, ply_path, cloud_points, output_stl, 0.001);

    return ply_path;
}

void Simple3DBuilder::scale_ply_file(const std::string& input_ply_path, const std::string& output_ply_path, double scale) {
    if (!std::filesystem::exists(input_ply_path)) {
        throw std::invalid_argument("PLY 文件不存在: " + input_ply_path);
    }

    if (scale <= 0) {
        throw std::invalid_argument("缩放因子必须大于0");
    }

    // 使用 PCL 加载 PLY 文件
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>());

    if (pcl::io::loadPLYFile(input_ply_path, *cloud) == -1) {
        throw std::runtime_error("无法加载 PLY 文件: " + input_ply_path);
    }

    std::cout << "加载 PLY 文件: " << input_ply_path << std::endl;
    std::cout << "原始点数: " << cloud->points.size() << std::endl;

    // 应用缩放变换
    if (std::abs(scale - 1.0) > 1e-9) {
        for (auto& point : cloud->points) {
            point.x *= scale;
            point.y *= scale;
            point.z *= scale;
        }
        std::cout << "应用缩放因子: " << scale << std::endl;
    } else {
        std::cout << "缩放因子为1.0，不进行缩放" << std::endl;
    }

    // 确保输出目录存在
    std::filesystem::path output_path(output_ply_path);
    auto parent_dir = output_path.parent_path();
    if (!parent_dir.empty()) {
        std::filesystem::create_directories(parent_dir);
    }

    // 保存缩放后的 PLY 文件
    if (pcl::io::savePLYFileBinary(output_ply_path, *cloud) == -1) {
        throw std::runtime_error("无法保存 PLY 文件: " + output_ply_path);
    }

    std::cout << "PLY 文件已写入: " << output_ply_path << std::endl;
    std::cout << "最终点数: " << cloud->points.size() << std::endl;
}

void Simple3DBuilder::sample_step_to_ply(const std::string& step_path, const std::string& ply_path, int target_total_points, double scale) {
    TopoDS_Shape shape = load_step_shape(step_path);
    std::cout << "网格化完成，开始采样点云..." << std::endl;
    shape_to_ply(shape, ply_path, target_total_points, scale);
}

std::string Simple3DBuilder::join_models(
    const std::string& input1_path, const std::string& input2_path,
    double scale1, double scale2,
    double tx, double ty, double tz, double rx, double ry, double rz,
    size_t cloud_points, const std::string& output_dir, bool output_stl, const std::string& output_filename) {

    std::filesystem::create_directories(output_dir);

    // 加载两个模型
    std::cout << "加载第一个模型: " << input1_path << std::endl;
    TopoDS_Shape shape1 = load_shape_from_file(input1_path);

    std::cout << "加载第二个模型: " << input2_path << std::endl;
    TopoDS_Shape shape2 = load_shape_from_file(input2_path);

    // 应用缩放
    if (std::abs(scale1 - 1.0) > 1e-9) {
        std::cout << "对第一个模型应用缩放因子: " << scale1 << std::endl;
        shape1 = apply_scale_transform(shape1, scale1);
    }

    if (std::abs(scale2 - 1.0) > 1e-9) {
        std::cout << "对第二个模型应用缩放因子: " << scale2 << std::endl;
        shape2 = apply_scale_transform(shape2, scale2);
    }

    // 对第二个模型应用变换
    if (std::abs(tx) > 1e-9 || std::abs(ty) > 1e-9 || std::abs(tz) > 1e-9 ||
        std::abs(rx) > 1e-9 || std::abs(ry) > 1e-9 || std::abs(rz) > 1e-9) {
        std::cout << "对第二个模型应用变换: 平移(" << tx << ", " << ty << ", " << tz
                  << "), 旋转(" << rx << ", " << ry << ", " << rz << ")" << std::endl;
        shape2 = apply_transform(shape2, tx, ty, tz, rx, ry, rz);
    }

    // 拼接两个模型
    std::cout << "拼接两个模型..." << std::endl;
    TopoDS_Shape joined_shape = BRepAlgoAPI_Fuse(shape1, shape2).Shape();
    if (joined_shape.IsNull()) {
        throw std::runtime_error("模型拼接失败。");
    }

    // 生成输出文件名
    std::string base_filename;
    if (output_filename.empty()) {
        // 自动生成文件名
        std::filesystem::path path1(input1_path);
        std::filesystem::path path2(input2_path);
        std::stringstream ss;
        ss << "joined_" << path1.stem().string() << "_" << path2.stem().string();
        base_filename = ss.str();
    } else {
        base_filename = output_filename;
    }

    std::string ply_path = output_dir + "/" + base_filename + ".ply";
    std::string stl_path = output_dir + "/" + base_filename + ".stl";

    // 导出结果
    export_shape(joined_shape, stl_path, ply_path, cloud_points, output_stl, 1.0);

    return ply_path;
}

// ==================== 私有辅助方法实现 ====================

TopoDS_Shape Simple3DBuilder::apply_scale_transform(const TopoDS_Shape& shape, double scale) {
    if (std::abs(scale - 1.0) <= 1e-9) {
        return shape; // 不需要缩放
    }

    gp_Trsf scaleTrsf;
    scaleTrsf.SetScale(gp_Pnt(0, 0, 0), scale);
    BRepBuilderAPI_Transform scaleTransform(shape, scaleTrsf);

    std::cout << "应用缩放因子: " << scale << std::endl;
    return scaleTransform.Shape();
}

void Simple3DBuilder::mesh_shape(const TopoDS_Shape& shape) {
    BRepMesh_IncrementalMesh mesh(shape, Simple3DBuilder::MESH_LINEAR_DEFLECTION, false, Simple3DBuilder::MESH_ANGULAR_DEFLECTION);
    if (!mesh.IsDone()) {
        throw std::runtime_error("网格生成失败。");
    }
}

void Simple3DBuilder::shape_to_ply(const TopoDS_Shape& input_shape, const std::string& ply_path, int target_total_points, double scale) {
    // 应用缩放变换
    TopoDS_Shape shape = apply_scale_transform(input_shape, scale);

    // 网格化几何体
    mesh_shape(shape);

    // 采样表面点云
    sample_shape_surface_to_ply(shape, ply_path, target_total_points);
}

TopoDS_Shape Simple3DBuilder::load_stl_shape(const std::string& stl_path) {
    if (!std::filesystem::exists(stl_path)) {
        throw std::invalid_argument("STL 文件不存在: " + stl_path);
    }

    TopoDS_Shape shape;
    StlAPI_Reader reader;

    if (!reader.Read(shape, stl_path.c_str()) || shape.IsNull()) {
        throw std::runtime_error("无法从 STL 文件读取形状：" + stl_path);
    }

    std::cout << "成功加载 STL 文件: " << stl_path << std::endl;
    return shape;
}

TopoDS_Shape Simple3DBuilder::load_step_shape(const std::string& step_path) {
    if (!std::filesystem::exists(step_path)) {
        throw std::invalid_argument("STEP 文件不存在: " + step_path);
    }

    // 使用 OCCT STEPControl_Reader 读取 STEP 文件
    STEPControl_Reader reader;
    IFSelect_ReturnStatus status = reader.ReadFile(step_path.c_str());

    if (status != IFSelect_RetDone) {
        throw std::runtime_error("无法读取 STEP 文件: " + step_path);
    }

    std::cout << "成功读取 STEP 文件: " << step_path << std::endl;

    // 转换所有根实体
    Standard_Integer nb_roots = reader.NbRootsForTransfer();
    std::cout << "STEP 文件中的根实体数量: " << nb_roots << std::endl;

    if (nb_roots == 0) {
        throw std::runtime_error("STEP 文件中没有可转换的实体");
    }

    Standard_Integer nb_trans = reader.TransferRoots();
    std::cout << "成功转换的实体数量: " << nb_trans << std::endl;

    if (nb_trans == 0) {
        throw std::runtime_error("无法转换 STEP 文件中的任何实体");
    }

    // 获取转换后的形状
    TopoDS_Shape shape = reader.OneShape();
    if (shape.IsNull()) {
        throw std::runtime_error("转换后的形状为空");
    }

    std::cout << "成功获取 STEP 形状，形状数量: " << reader.NbShapes() << std::endl;
    return shape;
}

void Simple3DBuilder::export_shape(const TopoDS_Shape& shape, const std::string& stl_path, const std::string& ply_path,
                                   size_t cloud_points, bool output_stl, double scale) {
    // 应用缩放变换
    TopoDS_Shape scaled_shape = apply_scale_transform(shape, scale);

    // 网格化几何体
    mesh_shape(scaled_shape);

    // 导出 STL
    if (output_stl) {
        StlAPI_Writer stl_writer;
        stl_writer.Write(scaled_shape, stl_path.c_str());
        std::cout << "STL 文件已写入: " << stl_path << std::endl;
    }

    // 导出 PLY
    sample_shape_surface_to_ply(scaled_shape, ply_path, cloud_points);
}

TopoDS_Shape Simple3DBuilder::load_shape_from_file(const std::string& file_path) {
    if (!std::filesystem::exists(file_path)) {
        throw std::invalid_argument("文件不存在: " + file_path);
    }

    std::filesystem::path fs_path(file_path);
    std::string extension = fs_path.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    if (extension == ".stl") {
        return load_stl_shape(file_path);
    } else if (extension == ".step" || extension == ".stp") {
        return load_step_shape(file_path);
    } else if (extension == ".ply") {
        // PLY文件需要特殊处理，因为OCCT不直接支持PLY格式
        // 这里我们抛出异常，建议用户先转换为STL或STEP格式
        throw std::invalid_argument("PLY文件不支持直接加载为几何形状。请先使用StlToPlyTool将PLY转换为STL格式，或使用STL/STEP格式的文件。");
    } else {
        throw std::invalid_argument("不支持的文件格式: " + extension + "。仅支持 .stl, .step, .stp 文件。");
    }
}

TopoDS_Shape Simple3DBuilder::apply_transform(const TopoDS_Shape& shape, double tx, double ty, double tz, double rx, double ry, double rz) {
    gp_Trsf transform;

    // 创建复合变换：先旋转，再平移
    gp_Trsf rotation;

    // 按照X-Y-Z顺序应用旋转
    if (std::abs(rx) > 1e-9) {
        gp_Trsf rx_transform;
        rx_transform.SetRotation(gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0)), rx);
        rotation = rotation * rx_transform;
    }

    if (std::abs(ry) > 1e-9) {
        gp_Trsf ry_transform;
        ry_transform.SetRotation(gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0)), ry);
        rotation = rotation * ry_transform;
    }

    if (std::abs(rz) > 1e-9) {
        gp_Trsf rz_transform;
        rz_transform.SetRotation(gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1)), rz);
        rotation = rotation * rz_transform;
    }

    // 应用平移
    if (std::abs(tx) > 1e-9 || std::abs(ty) > 1e-9 || std::abs(tz) > 1e-9) {
        gp_Trsf translation;
        translation.SetTranslation(gp_Vec(tx, ty, tz));
        transform = translation * rotation;
    } else {
        transform = rotation;
    }

    BRepBuilderAPI_Transform transformer(shape, transform);
    return transformer.Shape();
}
